<!DOCTYPE html>
<html id="html-root" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">تفاصيل المود - Modetaris</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎮%3C/text%3E%3C/svg%3E">
    <link href="https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS مدمج - نفس التصميم الأصلي */
        :root {
            --bg-color: #1a1a1a;
            --header-color: #FFA500;
            --text-color: #ffffff;
            --button-color: #FFA500;
            --border-color: #333333;
            --accent-color: #FFD700;
            --card-color: #2D2D2D;
            --shadow-color: rgba(0,0,0,0.3);
            --font-family: 'Press Start 2P', monospace;
            --border-radius: 8px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
            padding: 1rem;
        }

        header h1 {
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            letter-spacing: 1px;
            text-align: center;
            font-size: 1.5rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 1rem;
            padding-bottom: 6rem;
        }

        /* Loading and Error States */
        .loading-screen {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 50;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--accent-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #ff4444;
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            margin: 20px;
        }

        /* Mod Container */
        .mod-container {
            background-color: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
            padding: 1rem;
        }

        .mod-header {
            background-color: var(--card-color);
            color: var(--text-color);
            padding: 15px;
            text-align: center;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 1rem;
        }

        .mod-title {
            font-size: 1.25rem;
            margin: 0;
        }

        /* Image Display */
        .image-container {
            position: relative;
            width: 100%;
            padding-top: 56.25%; /* 16:9 aspect ratio */
            margin-bottom: 1rem;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .main-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: var(--border-radius);
        }

        /* Info Grid */
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-item {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
        }

        .info-label {
            font-size: 12px;
            color: var(--accent-color);
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .info-value {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Description */
        .description-container {
            background: var(--card-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        /* Download Button */
        .download-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 1rem;
            background: var(--bg-color);
            border-top: 2px solid var(--border-color);
            display: flex;
            justify-content: center;
        }

        .download-button {
            background-color: var(--button-color);
            color: white;
            border: 2px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 200px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .download-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
            background-color: #FF8C00;
        }

        .download-button.downloading {
            background-color: #4CAF50;
            border-color: #45a049;
            cursor: not-allowed;
        }

        .download-button.downloaded {
            background-color: #2196F3;
            border-color: #1976D2;
        }

        /* Utility Classes */
        .hidden { display: none !important; }
        .text-center { text-align: center; }
        .fade-in { animation: fadeIn 0.5s ease-in; }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container { padding: 0.5rem; }
            .download-button { min-width: 180px; font-size: 14px; }
            header h1 { font-size: 1.25rem; }
            .mod-title { font-size: 1rem; }
        }

        /* Notification */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideInRight 0.3s ease-out;
        }

        .notification.success { background: #4CAF50; }
        .notification.error { background: #f44336; }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p style="color: white; margin-top: 1rem;">جاري تحميل بيانات المود...</p>
        </div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="loading-screen hidden">
        <div class="error-message" style="max-width: 400px;">
            <h2 style="margin-bottom: 1rem;">❌ خطأ في التحميل</h2>
            <p id="error-message-text">حدث خطأ أثناء تحميل بيانات المود</p>
            <button onclick="location.reload()" style="margin-top: 1rem; padding: 10px 20px; background: #fff; color: #f44336; border: none; border-radius: 4px; cursor: pointer;">
                🔄 إعادة المحاولة
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="hidden">
        <!-- Header -->
        <header>
            <h1 id="site-name">Modetaris</h1>
        </header>

        <div class="container">
            <!-- Mod Title -->
            <div class="mod-header">
                <h1 id="mod-title" class="mod-title">جاري التحميل...</h1>
            </div>

            <!-- Main Image -->
            <div class="mod-container">
                <div class="image-container">
                    <img id="main-mod-image" class="main-image" src="" alt="صورة المود">
                </div>
            </div>

            <!-- Mod Info -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">الإصدار</div>
                    <div id="mod-version" class="info-value">جاري التحميل...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">تصنيف المود</div>
                    <div id="mod-category" class="info-value">جاري التحميل...</div>
                </div>
            </div>

            <!-- Description -->
            <div class="description-container">
                <div class="info-label text-center">الوصف</div>
                <div id="mod-description" class="info-value text-center" style="margin-top: 10px;">جاري تحميل الوصف...</div>
            </div>
        </div>

        <!-- Download Button -->
        <div class="download-section">
            <a id="download-button" class="download-button" href="#" onclick="handleDownload(event)">
                <span id="download-icon">📥</span>
                <span id="download-text">تحميل المود</span>
            </a>
        </div>
    </div>

    <script>
        // JavaScript مدمج - جميع الوظائف
        let modData = null;
        let modId = '';
        let lang = 'ar';
        let isDownloading = false;
        let isDownloaded = false;

        // إعدادات Supabase المصححة
        const SUPABASE_URL = "https://ytqxxodyecdeosnqoure.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cXh4b2R5ZWNkZW9zbnFvdXJlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NTI2MTEwNSwiZXhwIjoyMDYwODM3MTA1fQ._BQpMA9YZpXCjvpoNRK2QdoecsE5VQsr3AN2DJhj2rw";

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        function initializePage() {
            extractUrlParameters();
            loadPageData();
        }

        function extractUrlParameters() {
            const urlParams = new URLSearchParams(window.location.search);
            modId = urlParams.get('id') || getModIdFromPath() || '1';
            lang = urlParams.get('lang') || 'ar';
            
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
        }

        function getModIdFromPath() {
            const path = window.location.pathname;
            const parts = path.split('/').filter(part => part);
            if (parts.length > 0 && parts[0] === 'mod' && parts[1]) {
                return parts[1];
            }
            return null;
        }

        async function loadPageData() {
            try {
                await loadModData();
                displayModData();
                hideLoadingScreen();
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                showErrorScreen(error.message);
            }
        }

        async function loadModData() {
            try {
                console.log('جاري تحميل بيانات المود:', modId);
                
                // تجربة الاتصال بـ Supabase مع headers محسنة
                const response = await fetch(`${SUPABASE_URL}/rest/v1/mods?id=eq.${modId}&select=*`, {
                    method: 'GET',
                    headers: {
                        'apikey': SUPABASE_KEY,
                        'Authorization': `Bearer ${SUPABASE_KEY}`,
                        'Content-Type': 'application/json',
                        'Prefer': 'return=representation'
                    }
                });

                console.log('استجابة Supabase:', response.status, response.statusText);

                if (!response.ok) {
                    // إذا فشل Supabase، استخدم بيانات تجريبية
                    console.warn('فشل الاتصال بـ Supabase، استخدام بيانات تجريبية');
                    modData = createSampleModData();
                    return;
                }

                const data = await response.json();
                console.log('بيانات من Supabase:', data);

                if (!data || data.length === 0) {
                    console.warn('لا توجد بيانات للمود، استخدام بيانات تجريبية');
                    modData = createSampleModData();
                    return;
                }

                modData = data[0];
                console.log('تم تحميل بيانات المود بنجاح:', modData);

            } catch (error) {
                console.error('خطأ في الاتصال بـ Supabase:', error);
                console.log('استخدام بيانات تجريبية بدلاً من ذلك');
                modData = createSampleModData();
            }
        }

        function createSampleModData() {
            const sampleMods = {
                '1': {
                    id: 1,
                    name: 'Lucky Block Mod',
                    description: 'مود الكتل المحظوظة يضيف كتل جديدة مليئة بالمفاجآت والكنوز. كل كتلة تحتوي على مكافآت أو تحديات مختلفة!',
                    version: '1.20.1',
                    category: 'addons',
                    download_url: 'https://example.com/download/lucky-block.mcpack',
                    image_urls: ['https://via.placeholder.com/800x450/4F46E5/FFFFFF?text=Lucky+Block+Mod']
                },
                '2': {
                    id: 2,
                    name: 'Dragon Mounts Mod',
                    description: 'مود ركوب التنانين يتيح لك ترويض وركوب التنانين في عالم ماينكرافت. استكشف السماء مع رفيقك التنين!',
                    version: '1.19.4',
                    category: 'addons',
                    download_url: 'https://example.com/download/dragon-mounts.mcpack',
                    image_urls: ['https://via.placeholder.com/800x450/7C3AED/FFFFFF?text=Dragon+Mounts']
                }
            };

            return sampleMods[modId] || sampleMods['1'];
        }

        function displayModData() {
            if (!modData) return;

            // تحديث العنوان
            document.getElementById('mod-title').textContent = modData.name || 'N/A';
            document.getElementById('page-title').textContent = `${modData.name} - تفاصيل المود`;

            // تحديث الصورة
            const imageUrl = (modData.image_urls && modData.image_urls.length > 0) ? 
                modData.image_urls[0] : 
                'https://via.placeholder.com/800x450/333/FFF?text=No+Image';
            document.getElementById('main-mod-image').src = imageUrl;

            // تحديث المعلومات
            document.getElementById('mod-version').textContent = modData.version || 'N/A';

            // تحديث التصنيف
            const categoryNames = {
                'addons': 'إضافات',
                'shaders': 'شيدرات',
                'texture_packs': 'حزم النسيج',
                'seeds': 'بذور',
                'maps': 'خرائط'
            };
            const categoryName = categoryNames[modData.category] || 'غير محدد';
            document.getElementById('mod-category').textContent = categoryName;

            // تحديث الوصف
            const description = modData.description || 'لا يوجد وصف متاح';
            document.getElementById('mod-description').textContent = description;

            // تحديث رابط التحميل
            const downloadButton = document.getElementById('download-button');
            if (modData.download_url) {
                downloadButton.href = modData.download_url;
            }
        }

        function handleDownload(event) {
            event.preventDefault();
            
            if (isDownloading || isDownloaded) return;

            const downloadButton = document.getElementById('download-button');
            const downloadIcon = document.getElementById('download-icon');
            const downloadText = document.getElementById('download-text');

            // بدء التحميل
            isDownloading = true;
            downloadButton.classList.add('downloading');
            downloadIcon.textContent = '⏳';
            downloadText.textContent = 'جاري التحميل...';

            // فتح رابط التحميل
            if (modData && modData.download_url) {
                window.open(modData.download_url, '_blank');
            }

            // محاكاة انتهاء التحميل
            setTimeout(() => {
                isDownloading = false;
                isDownloaded = true;
                downloadButton.classList.remove('downloading');
                downloadButton.classList.add('downloaded');
                downloadIcon.textContent = '✅';
                downloadText.textContent = 'تم التحميل';
                
                showNotification('تم بدء التحميل بنجاح!', 'success');
            }, 2000);
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        function hideLoadingScreen() {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('main-content').classList.remove('hidden');
            document.getElementById('main-content').classList.add('fade-in');
        }

        function showErrorScreen(message) {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('error-message-text').textContent = message;
            document.getElementById('error-screen').classList.remove('hidden');
        }
    </script>

    <!-- تحميل الملف المُصلح -->
    <script src="script_fixed.js"></script>
</body>
</html>
